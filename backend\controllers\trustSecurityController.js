const BuyerProtection = require('../models/BuyerProtection');
const Dispute = require('../models/Dispute');
const AbuseReport = require('../models/AbuseReport');
const Compliance = require('../models/Compliance');
const ProductReview = require('../models/ProductReview');
const Order = require('../models/Order');
const User = require('../models/User');
const Vendor = require('../models/Vendor');

// @desc    Create buyer protection for order
// @route   POST /api/trust/buyer-protection
// @access  Private
exports.createBuyerProtection = async (req, res) => {
  try {
    const {
      orderId,
      protectionType,
      coverageAmount,
      coveragePercentage = 100
    } = req.body;

    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Check if user owns the order
    if (order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to create protection for this order'
      });
    }

    // Check if protection already exists
    const existingProtection = await BuyerProtection.findOne({ order: orderId });
    if (existingProtection) {
      return res.status(400).json({
        success: false,
        message: 'Buyer protection already exists for this order'
      });
    }

    // Get vendor from order
    const vendor = await Vendor.findOne({ user: order.vendor });

    const protection = new BuyerProtection({
      order: orderId,
      buyer: req.user.id,
      vendor: vendor._id,
      protectionType,
      coverage: {
        amount: coverageAmount || order.total,
        percentage: coveragePercentage
      },
      terms: {
        expiryDate: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000), // 90 days
        claimDeadline: new Date(Date.now() + 60 * 24 * 60 * 60 * 1000), // 60 days
        conditions: [
          'Item must be as described',
          'Item must be delivered within specified timeframe',
          'Item must be in working condition'
        ],
        exclusions: [
          'Damage due to misuse',
          'Normal wear and tear',
          'Items used beyond return period'
        ]
      },
      paymentGateway: {
        provider: order.paymentMethod || 'stripe',
        transactionId: order.transactionId
      }
    });

    await protection.save();

    await protection.addTimelineEntry(
      'protection_created',
      'Buyer protection activated',
      req.user.id
    );

    res.status(201).json({
      success: true,
      data: protection,
      message: 'Buyer protection created successfully'
    });
  } catch (error) {
    console.error('Error creating buyer protection:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Activate buyer protection claim
// @route   POST /api/trust/buyer-protection/:id/claim
// @access  Private
exports.activateClaim = async (req, res) => {
  try {
    const {
      reason,
      description,
      requestedAction,
      requestedAmount
    } = req.body;

    const protection = await BuyerProtection.findById(req.params.id);
    if (!protection) {
      return res.status(404).json({
        success: false,
        message: 'Buyer protection not found'
      });
    }

    // Check if user owns the protection
    if (protection.buyer.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to claim this protection'
      });
    }

    // Check if protection is still valid
    if (!protection.isValid()) {
      return res.status(400).json({
        success: false,
        message: 'Protection has expired or is no longer valid'
      });
    }

    // Handle evidence upload (already processed by middleware)
    let evidence = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (file.cloudinaryUrl) {
          evidence.push({
            type: 'image',
            url: file.cloudinaryUrl,
            description: file.originalname
          });
        }
      }
    }

    await protection.activateClaim(reason, description, evidence, requestedAction, requestedAmount);

    res.status(200).json({
      success: true,
      data: protection,
      message: 'Claim activated successfully'
    });
  } catch (error) {
    console.error('Error activating claim:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create dispute
// @route   POST /api/trust/disputes
// @access  Private
exports.createDispute = async (req, res) => {
  try {
    const {
      orderId,
      type,
      category,
      title,
      description,
      amount,
      requestedResolution
    } = req.body;

    const order = await Order.findById(orderId);
    if (!order) {
      return res.status(404).json({
        success: false,
        message: 'Order not found'
      });
    }

    // Determine respondent based on who initiated
    let respondent, vendor;
    if (order.user.toString() === req.user.id) {
      // Buyer initiating dispute against vendor
      vendor = await Vendor.findOne({ user: order.vendor });
      respondent = order.vendor;
    } else if (order.vendor.toString() === req.user.id) {
      // Vendor initiating dispute against buyer
      respondent = order.user;
      vendor = await Vendor.findOne({ user: req.user.id });
    } else {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to create dispute for this order'
      });
    }

    const dispute = new Dispute({
      order: orderId,
      initiator: req.user.id,
      respondent,
      vendor: vendor._id,
      type,
      category,
      details: {
        title,
        description,
        amount,
        requestedResolution
      }
    });

    await dispute.save();

    await dispute.addTimelineEntry(
      'dispute_created',
      'Dispute initiated',
      req.user.id
    );

    res.status(201).json({
      success: true,
      data: dispute,
      message: 'Dispute created successfully'
    });
  } catch (error) {
    console.error('Error creating dispute:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Add message to dispute
// @route   POST /api/trust/disputes/:id/messages
// @access  Private
exports.addDisputeMessage = async (req, res) => {
  try {
    const { message } = req.body;

    const dispute = await Dispute.findById(req.params.id);
    if (!dispute) {
      return res.status(404).json({
        success: false,
        message: 'Dispute not found'
      });
    }

    // Check if user is involved in dispute
    if (dispute.initiator.toString() !== req.user.id &&
        dispute.respondent.toString() !== req.user.id &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to add message to this dispute'
      });
    }

    // Determine sender type
    let senderType = 'admin';
    if (dispute.initiator.toString() === req.user.id) {
      senderType = 'buyer';
    } else if (dispute.respondent.toString() === req.user.id) {
      senderType = 'vendor';
    }

    // Handle attachments (already processed by middleware)
    let attachments = [];
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (file.cloudinaryUrl) {
          attachments.push(file.cloudinaryUrl);
        }
      }
    }

    await dispute.addMessage(req.user.id, senderType, message, attachments);

    res.status(200).json({
      success: true,
      data: dispute,
      message: 'Message added successfully'
    });
  } catch (error) {
    console.error('Error adding dispute message:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create abuse report
// @route   POST /api/trust/abuse-reports
// @access  Private
exports.createAbuseReport = async (req, res) => {
  try {
    const {
      reportedUserId,
      reportedVendorId,
      contentType,
      contentId,
      contentUrl,
      category,
      subcategory,
      description
    } = req.body;

    // Get content snapshot for evidence
    let contentSnapshot = '';
    try {
      // This would fetch the actual content based on type and ID
      // Implementation depends on your content models
      contentSnapshot = `Content reported at ${new Date().toISOString()}`;
    } catch (err) {
      console.log('Could not capture content snapshot:', err);
    }

    const report = new AbuseReport({
      reporter: req.user.id,
      reportedUser: reportedUserId,
      reportedVendor: reportedVendorId,
      reportedContent: {
        contentType,
        contentId,
        contentUrl,
        contentSnapshot
      },
      category,
      subcategory,
      description,
      severity: category === 'fraud' || category === 'scam' ? 'high' : 'medium'
    });

    // Handle evidence upload (already processed by middleware)
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (file.cloudinaryUrl) {
          report.evidence.push({
            type: 'screenshot',
            url: file.cloudinaryUrl,
            description: file.originalname
          });
        }
      }
    }

    await report.save();

    res.status(201).json({
      success: true,
      data: report,
      message: 'Abuse report submitted successfully'
    });
  } catch (error) {
    console.error('Error creating abuse report:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Create verified product review
// @route   POST /api/trust/reviews
// @access  Private
exports.createVerifiedReview = async (req, res) => {
  try {
    const {
      productId,
      orderId,
      rating,
      title,
      comment,
      pros,
      cons,
      aspects
    } = req.body;

    // Verify order and product
    const order = await Order.findById(orderId);
    if (!order || order.user.toString() !== req.user.id) {
      return res.status(403).json({
        success: false,
        message: 'Invalid order or not authorized'
      });
    }

    // Check if product was in the order
    const productInOrder = order.items.some(item =>
      item.product.toString() === productId
    );

    if (!productInOrder) {
      return res.status(400).json({
        success: false,
        message: 'Product was not in this order'
      });
    }

    // Check if review already exists
    const existingReview = await ProductReview.findOne({
      user: req.user.id,
      product: productId
    });

    if (existingReview) {
      return res.status(400).json({
        success: false,
        message: 'You have already reviewed this product'
      });
    }

    const review = new ProductReview({
      product: productId,
      user: req.user.id,
      order: orderId,
      rating,
      title,
      comment,
      pros,
      cons,
      aspects,
      isVerifiedPurchase: true
    });

    // Handle image uploads (already processed by middleware)
    if (req.files && req.files.length > 0) {
      for (const file of req.files) {
        if (file.cloudinaryUrl) {
          review.images.push(file.cloudinaryUrl);
        }
      }
    }

    // Verify purchase and add tags
    await review.verifyPurchase(order, 'order_match');

    // Add buyer tags based on user history
    const userOrderCount = await Order.countDocuments({ user: req.user.id });
    if (userOrderCount >= 10) {
      review.addBuyerTag('frequent_buyer');
    }

    const userReviewCount = await ProductReview.countDocuments({ user: req.user.id });
    if (userReviewCount >= 5) {
      review.addBuyerTag('expert_reviewer');
    }

    await review.save();

    res.status(201).json({
      success: true,
      data: review,
      message: 'Verified review created successfully'
    });
  } catch (error) {
    console.error('Error creating verified review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user compliance status
// @route   GET /api/trust/compliance
// @access  Private
exports.getComplianceStatus = async (req, res) => {
  try {
    let compliance = await Compliance.findOne({ user: req.user.id });

    if (!compliance) {
      // Create compliance record for new user
      compliance = new Compliance({
        user: req.user.id,
        gdpr: {
          consentGiven: false,
          dataRetention: {
            retentionUntil: new Date(Date.now() + 2555 * 24 * 60 * 60 * 1000) // 7 years
          }
        }
      });
      await compliance.save();
    }

    const status = compliance.getComplianceStatus();

    res.status(200).json({
      success: true,
      data: {
        compliance,
        status
      }
    });
  } catch (error) {
    console.error('Error getting compliance status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Give GDPR consent
// @route   POST /api/trust/compliance/gdpr-consent
// @access  Private
exports.giveGDPRConsent = async (req, res) => {
  try {
    const { purposes, method = 'explicit', version = '1.0' } = req.body;

    let compliance = await Compliance.findOne({ user: req.user.id });

    if (!compliance) {
      compliance = new Compliance({ user: req.user.id });
    }

    await compliance.giveGDPRConsent(purposes, method, version);

    await compliance.addAuditEntry(
      'gdpr_consent_given',
      req.user.id,
      req.ip,
      req.get('User-Agent'),
      { purposes, method, version }
    );

    res.status(200).json({
      success: true,
      data: compliance,
      message: 'GDPR consent recorded successfully'
    });
  } catch (error) {
    console.error('Error giving GDPR consent:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get user disputes
// @route   GET /api/trust/disputes
// @access  Private
exports.getUserDisputes = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 10,
      status,
      type
    } = req.query;

    let query = {
      $or: [
        { initiator: req.user.id },
        { respondent: req.user.id }
      ]
    };

    if (status) query.status = status;
    if (type) query.type = type;

    const disputes = await Dispute.find(query)
      .populate('initiator', 'name email')
      .populate('respondent', 'name email')
      .populate('order', 'orderNumber total')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await Dispute.countDocuments(query);

    res.status(200).json({
      success: true,
      data: disputes,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting user disputes:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get buyer protections
// @route   GET /api/trust/buyer-protection
// @access  Private
exports.getBuyerProtections = async (req, res) => {
  try {
    const protections = await BuyerProtection.find({ buyer: req.user.id })
      .populate('order', 'orderNumber total createdAt')
      .populate('vendor', 'businessName')
      .sort({ createdAt: -1 });

    res.status(200).json({
      success: true,
      data: protections
    });
  } catch (error) {
    console.error('Error getting buyer protections:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Mark review as helpful
// @route   POST /api/trust/reviews/:id/helpful
// @access  Private
exports.markReviewHelpful = async (req, res) => {
  try {
    const review = await ProductReview.findById(req.params.id);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.markHelpful(req.user.id);

    res.status(200).json({
      success: true,
      data: review,
      message: 'Review marked as helpful'
    });
  } catch (error) {
    console.error('Error marking review helpful:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Report review
// @route   POST /api/trust/reviews/:id/report
// @access  Private
exports.reportReview = async (req, res) => {
  try {
    const { reason } = req.body;

    const review = await ProductReview.findById(req.params.id);
    if (!review) {
      return res.status(404).json({
        success: false,
        message: 'Review not found'
      });
    }

    await review.reportReview(req.user.id, reason);

    res.status(200).json({
      success: true,
      data: review,
      message: 'Review reported successfully'
    });
  } catch (error) {
    console.error('Error reporting review:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Request data export (GDPR)
// @route   POST /api/trust/compliance/data-export
// @access  Private
exports.requestDataExport = async (req, res) => {
  try {
    let compliance = await Compliance.findOne({ user: req.user.id });

    if (!compliance) {
      compliance = new Compliance({ user: req.user.id });
    }

    await compliance.requestDataExport();

    await compliance.addAuditEntry(
      'data_export_requested',
      req.user.id,
      req.ip,
      req.get('User-Agent')
    );

    res.status(200).json({
      success: true,
      data: compliance,
      message: 'Data export request submitted successfully'
    });
  } catch (error) {
    console.error('Error requesting data export:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Request data deletion (GDPR)
// @route   POST /api/trust/compliance/data-deletion
// @access  Private
exports.requestDataDeletion = async (req, res) => {
  try {
    let compliance = await Compliance.findOne({ user: req.user.id });

    if (!compliance) {
      compliance = new Compliance({ user: req.user.id });
    }

    await compliance.requestDataDeletion();

    await compliance.addAuditEntry(
      'data_deletion_requested',
      req.user.id,
      req.ip,
      req.get('User-Agent')
    );

    res.status(200).json({
      success: true,
      data: compliance,
      message: 'Data deletion request submitted successfully'
    });
  } catch (error) {
    console.error('Error requesting data deletion:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Resolve dispute (Admin only)
// @route   PUT /api/trust/disputes/:id/resolve
// @access  Private (Admin)
exports.resolveDispute = async (req, res) => {
  try {
    const { resolutionType, outcome } = req.body;

    const dispute = await Dispute.findById(req.params.id);
    if (!dispute) {
      return res.status(404).json({
        success: false,
        message: 'Dispute not found'
      });
    }

    await dispute.resolve(req.user.id, resolutionType, outcome);

    res.status(200).json({
      success: true,
      data: dispute,
      message: 'Dispute resolved successfully'
    });
  } catch (error) {
    console.error('Error resolving dispute:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Escalate dispute
// @route   PUT /api/trust/disputes/:id/escalate
// @access  Private
exports.escalateDispute = async (req, res) => {
  try {
    const { reason, assignedTo } = req.body;

    const dispute = await Dispute.findById(req.params.id);
    if (!dispute) {
      return res.status(404).json({
        success: false,
        message: 'Dispute not found'
      });
    }

    // Check if user is involved in dispute
    if (dispute.initiator.toString() !== req.user.id &&
        dispute.respondent.toString() !== req.user.id &&
        req.user.role !== 'admin') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to escalate this dispute'
      });
    }

    await dispute.escalate(req.user.id, reason, assignedTo);

    res.status(200).json({
      success: true,
      data: dispute,
      message: 'Dispute escalated successfully'
    });
  } catch (error) {
    console.error('Error escalating dispute:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Get abuse reports (Admin only)
// @route   GET /api/trust/abuse-reports
// @access  Private (Admin)
exports.getAbuseReports = async (req, res) => {
  try {
    const {
      page = 1,
      limit = 20,
      status,
      category,
      severity
    } = req.query;

    let query = {};
    if (status) query.status = status;
    if (category) query.category = category;
    if (severity) query.severity = severity;

    const reports = await AbuseReport.find(query)
      .populate('reporter', 'name email')
      .populate('reportedUser', 'name email')
      .populate('reportedVendor', 'businessName')
      .sort({ createdAt: -1 })
      .limit(limit * 1)
      .skip((page - 1) * limit);

    const total = await AbuseReport.countDocuments(query);

    res.status(200).json({
      success: true,
      data: reports,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (error) {
    console.error('Error getting abuse reports:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Process abuse report (Admin only)
// @route   PUT /api/trust/abuse-reports/:id/process
// @access  Private (Admin)
exports.processAbuseReport = async (req, res) => {
  try {
    const { actionType, description, duration } = req.body;

    const report = await AbuseReport.findById(req.params.id);
    if (!report) {
      return res.status(404).json({
        success: false,
        message: 'Abuse report not found'
      });
    }

    await report.addAction(actionType, description, req.user.id, duration);

    res.status(200).json({
      success: true,
      data: report,
      message: 'Abuse report processed successfully'
    });
  } catch (error) {
    console.error('Error processing abuse report:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

// @desc    Update KYC status
// @route   PUT /api/trust/compliance/kyc
// @access  Private
exports.updateKYCStatus = async (req, res) => {
  try {
    const { status, level, documents } = req.body;

    let compliance = await Compliance.findOne({ user: req.user.id });

    if (!compliance) {
      compliance = new Compliance({ user: req.user.id });
    }

    await compliance.updateKYCStatus(status, level);

    if (documents && documents.length > 0) {
      documents.forEach(doc => {
        compliance.addKYCDocument(doc.type, doc.documentId);
      });
    }

    await compliance.addAuditEntry(
      'kyc_status_updated',
      req.user.id,
      req.ip,
      req.get('User-Agent'),
      { status, level }
    );

    res.status(200).json({
      success: true,
      data: compliance,
      message: 'KYC status updated successfully'
    });
  } catch (error) {
    console.error('Error updating KYC status:', error);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
};

module.exports = {
  createBuyerProtection: exports.createBuyerProtection,
  activateClaim: exports.activateClaim,
  createDispute: exports.createDispute,
  addDisputeMessage: exports.addDisputeMessage,
  createAbuseReport: exports.createAbuseReport,
  createVerifiedReview: exports.createVerifiedReview,
  getComplianceStatus: exports.getComplianceStatus,
  giveGDPRConsent: exports.giveGDPRConsent,
  getUserDisputes: exports.getUserDisputes,
  getBuyerProtections: exports.getBuyerProtections,
  markReviewHelpful: exports.markReviewHelpful,
  reportReview: exports.reportReview,
  requestDataExport: exports.requestDataExport,
  requestDataDeletion: exports.requestDataDeletion,
  resolveDispute: exports.resolveDispute,
  escalateDispute: exports.escalateDispute,
  getAbuseReports: exports.getAbuseReports,
  processAbuseReport: exports.processAbuseReport,
  updateKYCStatus: exports.updateKYCStatus
};
