const multer = require('multer');
const path = require('path');
const fs = require('fs');
const { uploadToCloudinary } = require('../config/cloudinary');

// Generate a unique ID if uuid is not available
const generateUniqueId = () => {
  try {
    const { v4: uuidv4 } = require('uuid');
    return uuidv4();
  } catch (err) {
    // Fallback to timestamp-based ID if uuid is not available
    return `${Date.now()}-${Math.random().toString(36).substring(2, 15)}`;
  }
};

// Ensure upload directories exist
const createUploadDirs = () => {
  const dirs = [
    'uploads',
    'uploads/posts',
    'uploads/stories',
    'uploads/reels',
    'uploads/profiles',
    'uploads/messages',
    'uploads/products',
    'uploads/vendors',
    'uploads/trust',
    'uploads/recordings',
    'uploads/misc'
  ];

  dirs.forEach(dir => {
    const dirPath = path.join(__dirname, '..', dir);
    if (!fs.existsSync(dirPath)) {
      fs.mkdirSync(dirPath, { recursive: true });
      console.log(`Created directory: ${dirPath}`);
    }
  });
};

// Create upload directories on startup
createUploadDirs();

// Configure storage
const storage = multer.diskStorage({
  destination: function(req, file, cb) {
    let uploadPath = path.join(__dirname, '../uploads');

    // Determine the specific folder based on file type or route
    if (req.baseUrl.includes('/posts')) {
      uploadPath = path.join(uploadPath, 'posts');
    } else if (req.baseUrl.includes('/reels')) {
      uploadPath = path.join(uploadPath, 'reels');
    } else if (req.baseUrl.includes('/stories')) {
      uploadPath = path.join(uploadPath, 'stories');
    } else if (req.baseUrl.includes('/profile')) {
      uploadPath = path.join(uploadPath, 'profiles');
    } else if (req.baseUrl.includes('/products')) {
      uploadPath = path.join(uploadPath, 'products');
    } else if (req.baseUrl.includes('/messages')) {
      uploadPath = path.join(uploadPath, 'messages');
    } else if (req.baseUrl.includes('/vendors')) {
      uploadPath = path.join(uploadPath, 'vendors');
    } else if (req.baseUrl.includes('/trust')) {
      uploadPath = path.join(uploadPath, 'trust');
    } else if (req.baseUrl.includes('/recordings')) {
      uploadPath = path.join(uploadPath, 'recordings');
    } else {
      uploadPath = path.join(uploadPath, 'misc');
    }

    // Ensure the directory exists
    if (!fs.existsSync(uploadPath)) {
      fs.mkdirSync(uploadPath, { recursive: true });
    }

    cb(null, uploadPath);
  },
  filename: function(req, file, cb) {
    // Generate a unique filename with original extension
    const uniqueFilename = `${generateUniqueId()}-${Date.now()}${path.extname(file.originalname)}`;
    cb(null, uniqueFilename);
  }
});

// File filter
const fileFilter = (req, file, cb) => {
  // Define allowed file types based on route or purpose
  let allowedTypes = [];

  if (req.baseUrl.includes('/posts')) {
    // Allow both images and videos for posts
    allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'];
  } else if (req.baseUrl.includes('/profile') || req.baseUrl.includes('/products')) {
    // Only allow images for profiles and products
    allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp'];
  } else if (req.baseUrl.includes('/reels') || req.baseUrl.includes('/stories')) {
    // Allow both images and videos for reels and stories
    allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp', 'video/mp4', 'video/quicktime', 'video/x-msvideo', 'video/x-ms-wmv'];
  } else {
    // Default allowed types
    allowedTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/gif', 'image/webp', 'video/mp4'];
  }

  console.log(`Checking file type: ${file.mimetype} for route: ${req.baseUrl}`);

  if (allowedTypes.includes(file.mimetype)) {
    console.log(`File type ${file.mimetype} is allowed`);
    cb(null, true);
  } else {
    console.error(`File type ${file.mimetype} is not allowed for ${req.baseUrl}`);
    console.error(`Allowed types: ${allowedTypes.join(', ')}`);
    cb(new Error(`File type not allowed. Allowed types: ${allowedTypes.join(', ')}`), false);
  }
};

// Configure multer
const upload = multer({
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024 // 50MB max file size
  }
});

// Helper function to get file type (image or video)
const getFileType = (mimetype) => {
  if (mimetype.startsWith('image/')) {
    return 'image';
  } else if (mimetype.startsWith('video/')) {
    return 'video';
  } else {
    return 'unknown';
  }
};

// Process uploaded files and add them to the request
const processUploadedFiles = async (req, res, next) => {
  // Skip if no file was uploaded
  if (!req.file && (!req.files || req.files.length === 0)) {
    console.log('No file uploaded, skipping file processing');
    console.log('Request body:', req.body);
    console.log('Request headers:', req.headers);
    return next();
  }

  try {
    // Log Cloudinary configuration status
    console.log('Cloudinary configuration status for file upload:', {
      cloudName: process.env.CLOUDINARY_CLOUD_NAME ? 'Set' : 'Not set',
      apiKey: process.env.CLOUDINARY_API_KEY ? 'Set' : 'Not set',
      apiSecret: process.env.CLOUDINARY_API_SECRET ? 'Set' : 'Not set'
    });

    // Determine folder name based on route
    let folderName = 'misc';
    if (req.baseUrl.includes('/posts')) {
      folderName = 'posts';
    } else if (req.baseUrl.includes('/reels')) {
      folderName = 'reels';
    } else if (req.baseUrl.includes('/stories')) {
      folderName = 'stories';
    } else if (req.baseUrl.includes('/profile')) {
      folderName = 'profiles';
    } else if (req.baseUrl.includes('/products')) {
      folderName = 'products';
    } else if (req.baseUrl.includes('/messages')) {
      folderName = 'messages';
    } else if (req.baseUrl.includes('/vendors')) {
      folderName = 'vendors';
    } else if (req.baseUrl.includes('/trust')) {
      folderName = 'trust';
    } else if (req.baseUrl.includes('/recordings')) {
      folderName = 'recordings';
    }

    // Process single file upload
    if (req.file) {
      console.log(`Processing single file: ${req.file.originalname}`);

      // Determine resource type based on mimetype
      const resourceType = req.file.mimetype.startsWith('video/') ? 'video' : 'auto';

      // Determine folder based on route
      const cloudinaryFolder = req.baseUrl.includes('/profile-picture') ?
        'letstalk/profiles' :
        `letstalk/${folderName}`;

      console.log(`Uploading ${resourceType} to Cloudinary folder: ${cloudinaryFolder}`);

      // Upload with minimal options to avoid signature issues
      let result;
      try {
        // Use minimal options to avoid signature issues
        result = await uploadToCloudinary(req.file.path, cloudinaryFolder, {
          resource_type: resourceType
        });
      } catch (uploadError) {
        console.error('Error during Cloudinary upload:', uploadError);
        throw new Error(`Failed to upload file to Cloudinary: ${uploadError.message}`);
      }

      // Add Cloudinary info to request
      req.file.url = result.secure_url;
      req.file.cloudinaryUrl = result.secure_url;
      req.file.cloudinaryPublicId = result.public_id;
      req.file.fileType = getFileType(req.file.mimetype);
      req.fileUrl = result.secure_url;

      // Store additional Cloudinary metadata
      req.file.cloudinaryData = {
        publicId: result.public_id,
        version: result.version,
        signature: result.signature,
        format: result.format,
        resourceType: result.resource_type,
        bytes: result.bytes,
        width: result.width,
        height: result.height,
        duration: result.duration || null
      };

      // Handle video-specific data
      if (req.file.mimetype.startsWith('video/')) {
        // Store video-specific metadata
        req.file.videoData = {
          duration: result.duration || 0,
          bitRate: result.bit_rate || 0,
          videoCodec: result.video_codec || '',
          audioCodec: result.audio_codec || '',
          frameRate: result.frame_rate || 0
        };

        // Store thumbnail URL if available
        if (result.eager && result.eager.length > 1 && result.eager[1].format === 'jpg') {
          req.file.thumbnailUrl = result.eager[1].secure_url;
          console.log(`Video thumbnail URL: ${req.file.thumbnailUrl}`);
        } else {
          // Generate a thumbnail URL directly from the video URL
          const videoId = result.public_id;
          const thumbnailUrl = `https://res.cloudinary.com/${process.env.CLOUDINARY_CLOUD_NAME}/video/upload/f_jpg,q_auto,w_720/${videoId}.jpg`;
          req.file.thumbnailUrl = thumbnailUrl;
          console.log(`Generated video thumbnail URL: ${req.file.thumbnailUrl}`);
        }
      }

      console.log(`File uploaded to Cloudinary: ${result.secure_url}`);
      console.log(`File type: ${req.file.fileType}, Size: ${result.bytes} bytes`);

      if (req.file.fileType === 'video') {
        console.log(`Video duration: ${result.duration || 'unknown'} seconds`);
      }

      // Delete local file after upload
      fs.unlinkSync(req.file.path);
      console.log(`Local file deleted: ${req.file.path}`);
    }

    // Process multiple files upload
    if (req.files && req.files.length > 0) {
      console.log(`Processing ${req.files.length} files`);

      for (const file of req.files) {
        // Determine resource type based on mimetype
        const resourceType = file.mimetype.startsWith('video/') ? 'video' : 'auto';

        // Determine folder based on route
        const cloudinaryFolder = `letstalk/${folderName}`;

        console.log(`Uploading ${resourceType} to Cloudinary folder: ${cloudinaryFolder}`);

        // Upload with minimal options to avoid signature issues
        const result = await uploadToCloudinary(file.path, cloudinaryFolder, {
          resource_type: resourceType
        });

        // Add Cloudinary info to file object
        file.url = result.secure_url;
        file.cloudinaryUrl = result.secure_url;
        file.cloudinaryPublicId = result.public_id;
        file.fileType = getFileType(file.mimetype);

        // Store additional Cloudinary metadata
        file.cloudinaryData = {
          publicId: result.public_id,
          version: result.version,
          signature: result.signature,
          format: result.format,
          resourceType: result.resource_type,
          bytes: result.bytes,
          width: result.width,
          height: result.height,
          duration: result.duration || null
        };

        // Handle video-specific data
        if (file.mimetype.startsWith('video/')) {
          // Store video-specific metadata
          file.videoData = {
            duration: result.duration || 0,
            bitRate: result.bit_rate || 0,
            videoCodec: result.video_codec || '',
            audioCodec: result.audio_codec || '',
            frameRate: result.frame_rate || 0
          };

          // Store thumbnail URL if available
          if (result.eager && result.eager.length > 1 && result.eager[1].format === 'jpg') {
            file.thumbnailUrl = result.eager[1].secure_url;
            console.log(`Video thumbnail URL: ${file.thumbnailUrl}`);
          } else {
            // Generate a thumbnail URL directly from the video URL
            const videoId = result.public_id;
            const thumbnailUrl = `https://res.cloudinary.com/${process.env.CLOUDINARY_CLOUD_NAME}/video/upload/f_jpg,q_auto,w_720/${videoId}.jpg`;
            file.thumbnailUrl = thumbnailUrl;
            console.log(`Generated video thumbnail URL: ${file.thumbnailUrl}`);
          }
        }

        console.log(`File uploaded to Cloudinary: ${result.secure_url}`);
        console.log(`File type: ${file.fileType}, Size: ${result.bytes} bytes`);

        if (file.fileType === 'video') {
          console.log(`Video duration: ${result.duration || 'unknown'} seconds`);
        }

        // Delete local file after upload
        fs.unlinkSync(file.path);
        console.log(`Local file deleted: ${file.path}`);
      }
    }

    next();
  } catch (err) {
    console.error('Error processing uploaded files:', err);
    console.error('Error details:', err.message);

    if (err.stack) {
      console.error('Error stack:', err.stack);
    }

    // Log more details about the request
    console.log('Request URL:', req.originalUrl);
    console.log('Request method:', req.method);
    console.log('Request file field name:', req.file ? 'req.file exists' : 'no req.file');

    // Clean up any remaining local files
    try {
      if (req.file && fs.existsSync(req.file.path)) {
        fs.unlinkSync(req.file.path);
        console.log(`Cleaned up local file after error: ${req.file.path}`);
      }

      if (req.files && req.files.length > 0) {
        for (const file of req.files) {
          if (fs.existsSync(file.path)) {
            fs.unlinkSync(file.path);
            console.log(`Cleaned up local file after error: ${file.path}`);
          }
        }
      }
    } catch (cleanupError) {
      console.error('Error cleaning up local files:', cleanupError);
    }

    // Create a more user-friendly error message
    const userError = new Error('Failed to process uploaded file. Please try again.');
    userError.statusCode = 500;
    next(userError);
  }
};

module.exports = {
  upload,
  processUploadedFiles,
  getFileType
};
