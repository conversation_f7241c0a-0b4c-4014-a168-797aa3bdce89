const Post = require('../models/Post');
const User = require('../models/User');
const Follow = require('../models/Follow');
const Like = require('../models/Like');
const { createError } = require('../utils/error');
const mongoose = require('mongoose');
const { deleteFromCloudinary } = require('../config/cloudinary');

/**
 * Get all posts with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPosts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      includeUserDetails = true,
      includeComments = false,
      includeLikes = false,
      moodFilter
    } = req.query;

    // Build query
    let query = {};

    // Add mood filter if provided
    if (moodFilter) {
      query['emotions.emotion'] = moodFilter;
    }

    // Execute query with pagination
    const posts = await Post.find(query)
      .sort({ createdAt: -1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate(includeUserDetails ? 'user' : '', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Get total count for pagination
    const total = await Post.countDocuments(query);

    // If user is authenticated, check if they have liked each post
    let postsWithLikeInfo = [...posts];

    if (req.user) {
      // Get all post IDs
      const postIds = posts.map(post => post._id);

      // Find all likes by this user for these posts
      const userLikes = await Like.find({
        user: req.user.id,
        post: { $in: postIds }
      });

      // Create a Set of liked post IDs for faster lookup
      const likedPostIds = new Set(userLikes.map(like => like.post.toString()));

      // Add isLiked property to each post
      postsWithLikeInfo = posts.map(post => {
        const postObj = post.toObject();
        postObj.isLiked = likedPostIds.has(post._id.toString());
        return postObj;
      });

      console.log(`Added like info to ${postsWithLikeInfo.length} posts for user ${req.user.id}`);
    }

    res.status(200).json({
      success: true,
      count: postsWithLikeInfo.length,
      total,
      data: postsWithLikeInfo,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    console.error('Error in getPosts:', err);
    next(err);
  }
};

/**
 * Get a single post by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Convert to plain object to add custom properties
    const postObj = post.toObject();

    // If user is authenticated, check if they have liked the post
    if (req.user) {
      const existingLike = await Like.findOne({
        user: req.user.id,
        post: post._id
      });

      postObj.isLiked = !!existingLike;
      console.log(`Post ${post._id} isLiked status for user ${req.user.id}: ${postObj.isLiked}`);
    }

    res.status(200).json({
      success: true,
      data: postObj
    });
  } catch (err) {
    console.error('Error in getPost:', err);
    next(err);
  }
};

/**
 * Create a new post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.createPost = async (req, res, next) => {
  try {
    // Check if user is authenticated
    if (!req.user) {
      return res.status(401).json({
        success: false,
        error: 'User not authenticated. Please log in.'
      });
    }

    // Initialize req.body if it doesn't exist
    if (!req.body) {
      req.body = {};
    }

    // Add user to request body
    req.body.user = req.user.id;

    // Process emotions data if present
    if (req.body.emotions && typeof req.body.emotions === 'string') {
      try {
        const emotionsData = JSON.parse(req.body.emotions);

        // Validate and sanitize emotions data
        const sanitizedEmotions = emotionsData.map(emotion => {
          // Create a clean emotion object
          const sanitizedEmotion = {
            emotion: emotion.emotion,
            intensity: emotion.intensity || 5,
            position: {
              x: (emotion.position && typeof emotion.position.x === 'number') ? emotion.position.x : 50,
              y: (emotion.position && typeof emotion.position.y === 'number') ? emotion.position.y : 50
            }
          };

          // Add audio data if present
          if (emotion.audio && emotion.audio.enabled) {
            sanitizedEmotion.audio = {
              enabled: true,
              volume: emotion.audio.volume || 0.5
            };

            // Add cue if present
            if (emotion.audio.cue) {
              sanitizedEmotion.audio.cue = emotion.audio.cue.id || emotion.audio.cue;
            }
          }

          // Add visual effect if present
          if (emotion.visualEffect) {
            sanitizedEmotion.visualEffect = {
              enabled: emotion.visualEffect.enabled || false,
              type: emotion.visualEffect.type || 'none',
              intensity: emotion.visualEffect.intensity || 5
            };
          }

          return sanitizedEmotion;
        });

        // Replace the emotions data in the request body
        req.body.emotions = sanitizedEmotions;
      } catch (parseError) {
        console.error('Error parsing emotions data:', parseError);
        // If parsing fails, set emotions to empty array
        req.body.emotions = [];
      }
    }

    // Process media files if present
    if (req.files && req.files.length > 0) {
      console.log(`Media files detected in request: ${req.files.length} files`);

      // Log media file details for debugging
      req.files.forEach((file, index) => {
        console.log(`Media file ${index + 1}:`, {
          originalname: file.originalname,
          filename: file.filename,
          size: file.size,
          mimetype: file.mimetype,
          path: file.path,
          url: file.url,
        });
      });

      // Process files that were uploaded by multer
      req.body.media = req.files.map(file => {
        // Create base media object
        const mediaObject = {
          url: file.url,
          type: file.mimetype.startsWith('video/') ? 'video' : 'image',
          filename: file.filename,
          originalname: file.originalname,
          mimetype: file.mimetype,
          size: file.size,
          // Add Cloudinary metadata
          cloudinaryPublicId: file.cloudinaryPublicId,
          cloudinaryVersion: file.cloudinaryData?.version,
          cloudinarySignature: file.cloudinaryData?.signature,
          cloudinaryFormat: file.cloudinaryData?.format,
          width: file.cloudinaryData?.width,
          height: file.cloudinaryData?.height
        };

        // Add video-specific data if this is a video
        if (file.mimetype.startsWith('video/')) {
          mediaObject.thumbnailUrl = file.thumbnailUrl;
          mediaObject.duration = file.videoData?.duration;
          mediaObject.videoCodec = file.videoData?.videoCodec;
          mediaObject.audioCodec = file.videoData?.audioCodec;
        }

        return mediaObject;
      });

      console.log('Media processing complete:', {
        totalFiles: req.files.length,
        successfulUploads: req.body.media.length
      });
    } else {
      console.log('No media files in request');
    }

    // Create post
    console.log('Creating post with data:', {
      userId: req.body.user,
      caption: req.body.caption ? 'Present' : 'Not provided',
      mediaCount: req.body.media ? req.body.media.length : 0,
      emotionsCount: req.body.emotions ? req.body.emotions.length : 0
    });

    const post = await Post.create(req.body);
    console.log('Post created successfully with ID:', post._id);

    // Populate user details
    await post.populate('user', 'username name profilePicture isVerified');
    await post.populate('emotions.emotion', 'name color icon category');

    // Log media details for debugging
    if (post.media && post.media.length > 0) {
      console.log('Media in created post:', post.media.map(m => ({
        type: m.type,
        url: m.url,
        cloudinaryPublicId: m.cloudinaryPublicId,
        size: m.size,
        ...(m.type === 'video' ? {
          thumbnailUrl: m.thumbnailUrl,
          duration: m.duration,
          videoCodec: m.videoCodec,
          audioCodec: m.audioCodec,
          width: m.width,
          height: m.height
        } : {})
      })));

      // Log specific details for each media type
      const imageCount = post.media.filter(m => m.type === 'image').length;
      const videoCount = post.media.filter(m => m.type === 'video').length;

      console.log(`Post contains ${imageCount} images and ${videoCount} videos`);

      // Log video details if any
      if (videoCount > 0) {
        post.media.filter(m => m.type === 'video').forEach((video, index) => {
          console.log(`Video ${index + 1} details:`, {
            duration: video.duration ? `${video.duration.toFixed(2)} seconds` : 'unknown',
            size: video.size ? `${(video.size / (1024 * 1024)).toFixed(2)} MB` : 'unknown',
            resolution: video.width && video.height ? `${video.width}x${video.height}` : 'unknown',
            thumbnailUrl: video.thumbnailUrl || 'none'
          });
        });
      }
    } else {
      console.log('No media in created post');
    }

    res.status(201).json({
      success: true,
      data: post
    });
  } catch (err) {
    console.error('Error creating post:', err);

    // Provide a more detailed error message
    if (err.name === 'ValidationError') {
      const messages = Object.values(err.errors).map(val => val.message);
      return res.status(400).json({
        success: false,
        error: messages.join(', ')
      });
    }

    // Handle Cloudinary errors specifically
    if (err.message && err.message.includes('Invalid Signature')) {
      console.error('Cloudinary signature validation failed:', err.message);
      return res.status(500).json({
        success: false,
        error: 'Media upload failed: Signature validation error. Please try again.'
      });
    }

    // Handle file upload errors
    if (err.message && (
      err.message.includes('upload') ||
      err.message.includes('Cloudinary') ||
      err.message.includes('file')
    )) {
      console.error('File upload error:', err.message);
      return res.status(500).json({
        success: false,
        error: 'Media upload failed: ' + err.message
      });
    }

    // Handle other types of errors
    res.status(500).json({
      success: false,
      error: 'Server Error: ' + (err.message || 'Unknown error')
    });
  }
};

/**
 * Update a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.updatePost = async (req, res, next) => {
  try {
    let post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Check if user is post owner
    if (post.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to update this post'));
    }

    // Update post
    post = await Post.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    // Populate user details
    await post.populate('user', 'username name profilePicture isVerified');
    await post.populate('emotions.emotion', 'name color icon category');

    res.status(200).json({
      success: true,
      data: post
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deletePost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Check if user is post owner
    if (post.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this post'));
    }

    // Delete associated media files from Cloudinary
    if (post.media && post.media.length > 0) {
      for (const media of post.media) {
        try {
          if (media.cloudinaryPublicId) {
            // Delete from Cloudinary using public ID
            const resourceType = media.type === 'video' ? 'video' : 'image';
            await deleteFromCloudinary(media.cloudinaryPublicId, resourceType);
            console.log(`Deleted ${resourceType} from Cloudinary: ${media.cloudinaryPublicId}`);
          }
        } catch (err) {
          console.error(`Error deleting media from Cloudinary ${media.cloudinaryPublicId}:`, err);
          // Continue with deletion even if file removal fails
        }
      }
    }

    await post.remove();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get posts by user ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getUserPosts = async (req, res, next) => {
  try {
    const { page = 1, limit = 10 } = req.query;

    const posts = await Post.find({ user: req.params.userId })
      .sort({ createdAt: -1 })
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    const total = await Post.countDocuments({ user: req.params.userId });

    // If user is authenticated, check if they have liked each post
    let postsWithLikeInfo = [...posts];

    if (req.user) {
      // Get all post IDs
      const postIds = posts.map(post => post._id);

      // Find all likes by this user for these posts
      const userLikes = await Like.find({
        user: req.user.id,
        post: { $in: postIds }
      });

      // Create a Set of liked post IDs for faster lookup
      const likedPostIds = new Set(userLikes.map(like => like.post.toString()));

      // Add isLiked property to each post
      postsWithLikeInfo = posts.map(post => {
        const postObj = post.toObject();
        postObj.isLiked = likedPostIds.has(post._id.toString());
        return postObj;
      });

      console.log(`Added like info to ${postsWithLikeInfo.length} user posts for user ${req.user.id}`);
    }

    res.status(200).json({
      success: true,
      count: postsWithLikeInfo.length,
      total,
      data: postsWithLikeInfo,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    console.error('Error in getUserPosts:', err);
    next(err);
  }
};

/**
 * Like a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.likePost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Use the imported Like model

    // Check if post has already been liked by this user
    const existingLike = await Like.findOne({ user: req.user.id, post: post._id });
    if (existingLike) {
      return res.status(400).json({
        success: false,
        error: 'Post already liked'
      });
    }

    // Create a new like
    const newLike = await Like.create({
      user: req.user.id,
      post: post._id
    });

    // Emit socket event for real-time updates
    const socketEmitter = require('../utils/socketEmitter');
    socketEmitter.emitLike(req.user.id, 'post', post._id.toString(), post.user.toString());

    // Fetch the updated post with the new like count
    const updatedPost = await Post.findById(post._id)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Convert to plain object to add custom properties
    const updatedPostObj = updatedPost.toObject();

    // Add isLiked property (should be true since we just liked it)
    updatedPostObj.isLiked = true;

    // Get the current like count
    const likesCount = await Like.countDocuments({ post: post._id });
    updatedPostObj.likesCount = likesCount;

    console.log(`Post ${post._id} liked by user ${req.user.id}, current likes: ${likesCount}`);

    res.status(200).json({
      success: true,
      data: updatedPostObj
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Unlike a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.unlikePost = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Use the imported Like model

    // Check if post has been liked by this user
    const existingLike = await Like.findOne({ user: req.user.id, post: post._id });
    if (!existingLike) {
      return res.status(400).json({
        success: false,
        error: 'Post not liked yet'
      });
    }

    // Remove the like
    await Like.findByIdAndDelete(existingLike._id);

    // Fetch the updated post with the new like count
    const updatedPost = await Post.findById(post._id)
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Convert to plain object to add custom properties
    const updatedPostObj = updatedPost.toObject();

    // Add isLiked property (should be false since we just unliked it)
    updatedPostObj.isLiked = false;

    // Get the current like count
    const likesCount = await Like.countDocuments({ post: post._id });
    updatedPostObj.likesCount = likesCount;

    console.log(`Post ${post._id} unliked by user ${req.user.id}, current likes: ${likesCount}`);

    res.status(200).json({
      success: true,
      data: updatedPostObj
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get users who liked a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPostLikes = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Use the imported Like model

    // Find all likes for this post and populate user details
    const likes = await Like.find({ post: post._id })
      .populate('user', 'username name profilePicture isVerified');

    // Extract user data from likes
    const users = likes.map(like => like.user);

    res.status(200).json({
      success: true,
      count: users.length,
      data: users
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Add a comment to a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.addComment = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Create comment
    const comment = {
      user: req.user.id,
      text: req.body.text,
      emotions: req.body.emotions || []
    };

    // Add comment to post
    post.comments.push(comment);
    post.commentsCount = post.comments.length;

    await post.save();

    // Populate user details for the new comment
    await post.populate('comments.user', 'username name profilePicture isVerified');

    // Emit socket event for real-time updates
    const socketEmitter = require('../utils/socketEmitter');
    socketEmitter.emitComment(req.user.id, 'post', post._id.toString(), post.comments[post.comments.length - 1]._id.toString(), post.user.toString());

    res.status(201).json({
      success: true,
      data: post.comments[post.comments.length - 1]
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get comments for a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getPostComments = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id).populate('comments.user', 'username name profilePicture isVerified');

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    res.status(200).json({
      success: true,
      count: post.comments.length,
      data: post.comments
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Delete a comment from a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.deleteComment = async (req, res, next) => {
  try {
    const post = await Post.findById(req.params.id);

    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Find comment
    const comment = post.comments.id(req.params.commentId);

    if (!comment) {
      return next(createError(404, 'Comment not found'));
    }

    // Check if user is comment owner or post owner
    if (comment.user.toString() !== req.user.id && post.user.toString() !== req.user.id) {
      return next(createError(403, 'Not authorized to delete this comment'));
    }

    // Remove comment
    comment.remove();
    post.commentsCount = post.comments.length;

    await post.save();

    res.status(200).json({
      success: true,
      data: {}
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get trending posts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getTrendingPosts = async (req, res, next) => {
  try {
    const { limit = 12, timeFrame = 'week', includeUserDetails = true } = req.query;

    // Build time filter based on timeFrame
    const timeFilter = {};
    if (timeFrame !== 'all') {
      const now = new Date();
      let startDate;

      switch (timeFrame) {
        case 'day':
          startDate = new Date(now.setDate(now.getDate() - 1));
          break;
        case 'week':
          startDate = new Date(now.setDate(now.getDate() - 7));
          break;
        case 'month':
          startDate = new Date(now.setMonth(now.getMonth() - 1));
          break;
        default:
          startDate = new Date(now.setDate(now.getDate() - 7)); // Default to week
      }

      timeFilter.createdAt = { $gte: startDate };
    }

    // Find posts with the most likes and comments from the database
    const posts = await Post.find(timeFilter)
      .sort({ likesCount: -1, commentsCount: -1, createdAt: -1 })
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // If user is authenticated, check if they have liked each post
    let postsWithLikeInfo = [...posts];

    if (req.user) {
      // Get all post IDs
      const postIds = posts.map(post => post._id);

      // Find all likes by this user for these posts
      const userLikes = await Like.find({
        user: req.user.id,
        post: { $in: postIds }
      });

      // Create a Set of liked post IDs for faster lookup
      const likedPostIds = new Set(userLikes.map(like => like.post.toString()));

      // Add isLiked property to each post
      postsWithLikeInfo = posts.map(post => {
        const postObj = post.toObject();
        postObj.isLiked = likedPostIds.has(post._id.toString());
        return postObj;
      });

      console.log(`Added like info to ${postsWithLikeInfo.length} trending posts for user ${req.user.id}`);
    }

    // Return real posts from the database
    return res.status(200).json({
      success: true,
      count: postsWithLikeInfo.length,
      data: postsWithLikeInfo
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Get feed posts with pagination and filtering
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
/**
 * Get related posts
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.getRelatedPosts = async (req, res, next) => {
  try {
    const { id } = req.params;
    const { limit = 10 } = req.query;

    // Find the source post
    const sourcePost = await Post.findById(id).populate('emotions.emotion');
    if (!sourcePost) {
      return next(createError(404, 'Post not found'));
    }

    // Get emotion IDs from the source post
    const emotionIds = sourcePost.emotions.map(e => e.emotion._id);

    // Find posts with similar emotions, excluding the source post
    const relatedPosts = await Post.find({
      _id: { $ne: id },
      'emotions.emotion': { $in: emotionIds }
    })
      .sort({ createdAt: -1 })
      .limit(parseInt(limit))
      .populate('user', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // If not enough related posts, get some recent posts
    if (relatedPosts.length < parseInt(limit)) {
      const additionalPosts = await Post.find({
        _id: { $ne: id },
        'emotions.emotion': { $nin: emotionIds }
      })
        .sort({ createdAt: -1 })
        .limit(parseInt(limit) - relatedPosts.length)
        .populate('user', 'username name profilePicture isVerified')
        .populate('emotions.emotion', 'name color icon category');

      relatedPosts.push(...additionalPosts);
    }

    // If user is authenticated, check if they have liked each post
    let postsWithLikeInfo = [...relatedPosts];

    if (req.user) {
      // Get all post IDs
      const postIds = relatedPosts.map(post => post._id);

      // Find all likes by this user for these posts
      const userLikes = await Like.find({
        user: req.user.id,
        post: { $in: postIds }
      });

      // Create a Set of liked post IDs for faster lookup
      const likedPostIds = new Set(userLikes.map(like => like.post.toString()));

      // Add isLiked property to each post
      postsWithLikeInfo = relatedPosts.map(post => {
        const postObj = post.toObject();
        postObj.isLiked = likedPostIds.has(post._id.toString());
        return postObj;
      });

      console.log(`Added like info to ${postsWithLikeInfo.length} related posts for user ${req.user.id}`);
    }

    res.status(200).json({
      success: true,
      count: postsWithLikeInfo.length,
      data: postsWithLikeInfo
    });
  } catch (err) {
    next(err);
  }
};

/**
 * Record a view for a post
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Express next middleware function
 */
exports.viewPost = async (req, res, next) => {
  try {
    const { id } = req.params;

    // Find the post
    const post = await Post.findById(id);
    if (!post) {
      return next(createError(404, 'Post not found'));
    }

    // Get IP and user agent
    const ipAddress = req.ip || req.connection.remoteAddress;
    const userAgent = req.headers['user-agent'];

    // Create or update view
    let view;
    if (req.user) {
      // If user is logged in, check for existing view by user
      const View = require('../models/View');
      view = await View.findOne({ post: id, user: req.user.id });

      if (view) {
        // Update existing view
        view.viewDuration += 1; // Increment duration
        view.completedView = true;
        await view.save();
      } else {
        // Create new view
        view = await View.create({
          post: id,
          user: req.user.id,
          viewDuration: 1,
          completedView: true,
          ipAddress,
          userAgent
        });
      }
    } else {
      // For anonymous users, check by IP and user agent
      const View = require('../models/View');
      view = await View.findOne({
        post: id,
        ipAddress,
        userAgent,
        user: { $exists: false }
      });

      if (view) {
        // Update existing view
        view.viewDuration += 1;
        view.completedView = true;
        await view.save();
      } else {
        // Create new view
        view = await View.create({
          post: id,
          viewDuration: 1,
          completedView: true,
          ipAddress,
          userAgent
        });
      }
    }

    // Increment view count on post
    post.viewsCount = (post.viewsCount || 0) + 1;
    await post.save();

    res.status(200).json({
      success: true,
      data: { viewed: true }
    });
  } catch (err) {
    next(err);
  }
};

exports.getFeedPosts = async (req, res, next) => {
  try {
    const {
      page = 1,
      limit = 10,
      includeUserDetails = true,
      includeComments = false,
      includeLikes = false,
      moodFilter
    } = req.query;

    // Build query
    let query = {};

    // Add mood filter if provided
    if (moodFilter) {
      query['emotions.emotion'] = moodFilter;
    }

    // If user is authenticated, prioritize posts from followed users
    let followedUsers = [];
    if (req.user) {
      const follows = await Follow.find({ follower: req.user.id, status: 'accepted' });
      followedUsers = follows.map(follow => follow.following);

      // Include the user's own posts in the feed
      followedUsers.push(req.user.id);
    }

    // Execute query with pagination
    let postsQuery = Post.find(query);

    // If we have followed users, prioritize their posts
    if (followedUsers.length > 0) {
      postsQuery = postsQuery.sort({
        // First sort by whether the post is from a followed user
        user: { $in: followedUsers.map(id => id.toString()) } ? -1 : 1,
        // Then by creation date
        createdAt: -1
      });
    } else {
      // Otherwise just sort by creation date
      postsQuery = postsQuery.sort({ createdAt: -1 });
    }

    const posts = await postsQuery
      .skip((parseInt(page) - 1) * parseInt(limit))
      .limit(parseInt(limit))
      .populate(includeUserDetails ? 'user' : '', 'username name profilePicture isVerified')
      .populate('emotions.emotion', 'name color icon category');

    // Get total count for pagination
    const total = await Post.countDocuments(query);

    // If user is authenticated, check if they have liked each post
    let postsWithLikeInfo = [...posts];

    if (req.user) {
      // Get all post IDs
      const postIds = posts.map(post => post._id);

      // Find all likes by this user for these posts
      const userLikes = await Like.find({
        user: req.user.id,
        post: { $in: postIds }
      });

      // Create a Set of liked post IDs for faster lookup
      const likedPostIds = new Set(userLikes.map(like => like.post.toString()));

      // Add isLiked property to each post
      postsWithLikeInfo = posts.map(post => {
        const postObj = post.toObject();
        postObj.isLiked = likedPostIds.has(post._id.toString());
        return postObj;
      });

      console.log(`Added like info to ${postsWithLikeInfo.length} posts for user ${req.user.id}`);
    }

    res.status(200).json({
      success: true,
      count: postsWithLikeInfo.length,
      total,
      data: postsWithLikeInfo,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        totalPages: Math.ceil(total / parseInt(limit))
      }
    });
  } catch (err) {
    console.error('Error in getFeedPosts:', err);
    next(err);
  }
};
