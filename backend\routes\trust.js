const express = require('express');
const router = express.Router();
const { protect, authorize } = require('../middleware/auth');
const { upload, processUploadedFiles } = require('../middleware/fileUpload');
const {
  createBuyerProtection,
  activateClaim,
  createDispute,
  addDisputeMessage,
  createAbuseReport,
  createVerifiedReview,
  getComplianceStatus,
  giveGDPRConsent,
  getUserDisputes,
  getBuyerProtections,
  markReviewHelpful,
  reportReview,
  requestDataExport,
  requestDataDeletion,
  resolveDispute,
  escalateDispute,
  getAbuseReports,
  processAbuseReport,
  updateKYCStatus
} = require('../controllers/trustSecurityController');

// All routes require authentication
router.use(protect);

// Buyer Protection routes
router.post('/buyer-protection', createBuyerProtection);
router.get('/buyer-protection', getBuyerProtections);
router.post('/buyer-protection/:id/claim', upload.array('evidence', 5), processUploadedFiles, activateClaim);

// Dispute Resolution routes
router.get('/disputes', getUserDisputes);
router.post('/disputes', createDispute);
router.post('/disputes/:id/messages', upload.array('attachments', 3), processUploadedFiles, addDisputeMessage);

// Abuse Reporting routes
router.post('/abuse-reports', upload.array('evidence', 5), processUploadedFiles, createAbuseReport);

// Verified Reviews routes
router.post('/reviews', upload.array('images', 5), processUploadedFiles, createVerifiedReview);
router.post('/reviews/:id/helpful', markReviewHelpful);
router.post('/reviews/:id/report', reportReview);

// Compliance routes
router.get('/compliance', getComplianceStatus);
router.post('/compliance/gdpr-consent', giveGDPRConsent);
router.post('/compliance/data-export', requestDataExport);
router.post('/compliance/data-deletion', requestDataDeletion);
router.put('/compliance/kyc', updateKYCStatus);

// Dispute management routes
router.put('/disputes/:id/resolve', authorize('admin'), resolveDispute);
router.put('/disputes/:id/escalate', escalateDispute);

// Abuse report management routes (Admin only)
router.get('/abuse-reports', authorize('admin'), getAbuseReports);
router.put('/abuse-reports/:id/process', authorize('admin'), processAbuseReport);

module.exports = router;
