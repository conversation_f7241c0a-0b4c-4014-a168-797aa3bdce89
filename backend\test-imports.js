console.log('Testing imports...');

try {
  console.log('1. Testing express...');
  const express = require('express');
  console.log('✅ Express loaded');

  console.log('2. Testing dotenv...');
  const dotenv = require('dotenv');
  dotenv.config();
  console.log('✅ Dotenv loaded');

  console.log('3. Testing database connection...');
  const connectDB = require('./config/db');
  console.log('✅ DB config loaded');

  console.log('4. Testing port finder...');
  const { findAvailablePort } = require('./utils/portFinder');
  console.log('✅ Port finder loaded');

  console.log('5. Testing dynamic config...');
  const { generateDynamicConfig } = require('./utils/dynamicConfig');
  console.log('✅ Dynamic config loaded');

  console.log('6. Testing scheduler service...');
  const schedulerService = require('./services/schedulerService');
  console.log('✅ Scheduler service loaded');

  console.log('7. Testing error handler...');
  const errorHandler = require('./middleware/error');
  console.log('✅ Error handler loaded');

  console.log('8. Testing auth routes...');
  const authRoutes = require('./routes/auth');
  console.log('✅ Auth routes loaded');

  console.log('9. Testing posts routes...');
  const postsRoutes = require('./routes/posts');
  console.log('✅ Posts routes loaded');

  console.log('✅ All imports successful!');
} catch (error) {
  console.error('❌ Import error:', error.message);
  console.error('Stack:', error.stack);
}
