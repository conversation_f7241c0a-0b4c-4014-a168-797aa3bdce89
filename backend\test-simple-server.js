const express = require('express');
const dotenv = require('dotenv');
const { createServer } = require('http');

// Load environment variables
dotenv.config();

console.log('Starting simple test server...');

const app = express();
const httpServer = createServer(app);

app.use(express.json());

// Simple health check
app.get('/api/health', (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Simple test server is running',
    timestamp: new Date().toISOString()
  });
});

const PORT = process.env.PORT || 10001;

httpServer.listen(PORT, () => {
  console.log(`✅ Simple test server running on port ${PORT}`);
  console.log(`📡 Health check: http://localhost:${PORT}/api/health`);
});

// Handle errors
httpServer.on('error', (err) => {
  console.error('❌ Server error:', err);
});

process.on('unhandledRejection', (err) => {
  console.error('❌ Unhandled rejection:', err);
});
